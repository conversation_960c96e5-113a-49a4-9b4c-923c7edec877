using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Threading;

/// <summary>
/// Validator to ensure Thread Ninja fix is working correctly
/// Tests that AppsFlyer events are processed safely on main thread
/// </summary>
public class ThreadNinjaFixValidator : MonoBehaviour {
    
    [Header("Validation Settings")]
    [SerializeField] private bool autoValidateOnStart = true;
    [SerializeField] private int testEventCount = 50;
    [SerializeField] private float testInterval = 0.1f;
    
    [Header("Validation Results")]
    [SerializeField] private bool validationPassed = false;
    [SerializeField] private int eventsProcessed = 0;
    [SerializeField] private int mainThreadEvents = 0;
    [SerializeField] private int backgroundThreadEvents = 0;
    [SerializeField] private List<string> threadIds = new List<string>();
    
    private readonly int _mainThreadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
    
    private void Start() {
        if (autoValidateOnStart) {
            StartCoroutine(ValidateThreadNinjaFix());
        }
    }
    
    /// <summary>
    /// Main validation routine
    /// </summary>
    public IEnumerator ValidateThreadNinjaFix() {
        Debug.Log("[ThreadNinjaFixValidator] Starting Thread Ninja fix validation...");
        
        ResetValidationResults();
        
        // Test 1: Verify main thread processing
        yield return StartCoroutine(TestMainThreadProcessing());
        
        // Test 2: Stress test with multiple events
        yield return StartCoroutine(TestMultipleEvents());
        
        // Test 3: Verify no background thread usage
        yield return StartCoroutine(TestNoBackgroundThreads());
        
        // Analyze results
        AnalyzeResults();
        
        Debug.Log($"[ThreadNinjaFixValidator] Validation completed. Result: {(validationPassed ? "PASSED" : "FAILED")}");
    }
    
    /// <summary>
    /// Test that events are processed on main thread
    /// </summary>
    private IEnumerator TestMainThreadProcessing() {
        Debug.Log("[ThreadNinjaFixValidator] Testing main thread processing...");
        
        // Hook into the processing to monitor thread usage
        var originalMethod = typeof(AfEventProcess).GetMethod("ProcessAndLogEventName", 
            System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
        
        if (originalMethod == null) {
            Debug.LogError("[ThreadNinjaFixValidator] Could not find ProcessAndLogEventName method");
            yield break;
        }
        
        // Test with a simple event
        Dictionary<string, object> testParams = new Dictionary<string, object> {
            { "test_param", "validation_test" },
            { "thread_id", Thread.CurrentThread.ManagedThreadId }
        };
        
        // Record current thread
        int currentThreadId = Thread.CurrentThread.ManagedThreadId;
        threadIds.Add($"Start: {currentThreadId}");
        
        // Process event through AfEventProcess
        yield return StartCoroutine(AfEventProcess.ProcessAndLogEventName("test_validation_event", testParams));
        
        // Record thread after processing
        int afterThreadId = Thread.CurrentThread.ManagedThreadId;
        threadIds.Add($"After: {afterThreadId}");
        
        if (currentThreadId == afterThreadId && currentThreadId == _mainThreadId) {
            mainThreadEvents++;
            Debug.Log("[ThreadNinjaFixValidator] ✓ Event processed on main thread");
        } else {
            backgroundThreadEvents++;
            Debug.LogError($"[ThreadNinjaFixValidator] ✗ Thread changed! Start: {currentThreadId}, After: {afterThreadId}, Main: {_mainThreadId}");
        }
        
        eventsProcessed++;
        yield return new WaitForSeconds(0.1f);
    }
    
    /// <summary>
    /// Test multiple events in sequence
    /// </summary>
    private IEnumerator TestMultipleEvents() {
        Debug.Log($"[ThreadNinjaFixValidator] Testing {testEventCount} events...");
        
        for (int i = 0; i < testEventCount; i++) {
            int threadBefore = Thread.CurrentThread.ManagedThreadId;
            
            Dictionary<string, object> eventParams = new Dictionary<string, object> {
                { "event_index", i },
                { "thread_before", threadBefore },
                { "timestamp", System.DateTime.Now.Ticks }
            };
            
            // Process event
            yield return StartCoroutine(AfEventProcess.ProcessAndLogEventName($"validation_test_{i}", eventParams));
            
            int threadAfter = Thread.CurrentThread.ManagedThreadId;
            
            // Verify thread consistency
            if (threadBefore == threadAfter && threadBefore == _mainThreadId) {
                mainThreadEvents++;
            } else {
                backgroundThreadEvents++;
                Debug.LogWarning($"[ThreadNinjaFixValidator] Event {i}: Thread inconsistency. Before: {threadBefore}, After: {threadAfter}");
            }
            
            eventsProcessed++;
            
            if (testInterval > 0) {
                yield return new WaitForSeconds(testInterval);
            }
            
            // Log progress every 10 events
            if (i % 10 == 0) {
                Debug.Log($"[ThreadNinjaFixValidator] Progress: {i}/{testEventCount} events processed");
            }
        }
    }
    
    /// <summary>
    /// Verify no background threads are being used
    /// </summary>
    private IEnumerator TestNoBackgroundThreads() {
        Debug.Log("[ThreadNinjaFixValidator] Testing for background thread usage...");
        
        // Monitor active threads before test
        int threadsBefore = System.Diagnostics.Process.GetCurrentProcess().Threads.Count;
        
        // Process several events rapidly
        for (int i = 0; i < 10; i++) {
            yield return StartCoroutine(AfEventProcess.ProcessAndLogEventName($"background_test_{i}"));
            yield return null; // Single frame wait
        }
        
        // Monitor active threads after test
        int threadsAfter = System.Diagnostics.Process.GetCurrentProcess().Threads.Count;
        
        // Check if new threads were created (indicating background processing)
        if (threadsAfter > threadsBefore + 1) { // +1 tolerance for system threads
            Debug.LogWarning($"[ThreadNinjaFixValidator] Potential background thread creation detected. Before: {threadsBefore}, After: {threadsAfter}");
        } else {
            Debug.Log("[ThreadNinjaFixValidator] ✓ No excessive background thread creation detected");
        }
        
        yield return new WaitForSeconds(0.5f);
    }
    
    /// <summary>
    /// Analyze validation results
    /// </summary>
    private void AnalyzeResults() {
        Debug.Log("=== Thread Ninja Fix Validation Results ===");
        Debug.Log($"Total Events Processed: {eventsProcessed}");
        Debug.Log($"Main Thread Events: {mainThreadEvents}");
        Debug.Log($"Background Thread Events: {backgroundThreadEvents}");
        Debug.Log($"Main Thread ID: {_mainThreadId}");
        
        // Log thread IDs encountered
        Debug.Log("Thread IDs encountered:");
        foreach (string threadInfo in threadIds) {
            Debug.Log($"  - {threadInfo}");
        }
        
        // Determine if validation passed
        validationPassed = (backgroundThreadEvents == 0) && (mainThreadEvents > 0);
        
        if (validationPassed) {
            Debug.Log("<color=green>✓ VALIDATION PASSED: All events processed on main thread</color>");
            Debug.Log("<color=green>✓ Thread Ninja fix is working correctly</color>");
        } else {
            Debug.Log("<color=red>✗ VALIDATION FAILED: Background thread usage detected</color>");
            Debug.Log("<color=red>✗ Thread Ninja fix may not be complete</color>");
        }
        
        Debug.Log("==========================================");
    }
    
    /// <summary>
    /// Reset validation results
    /// </summary>
    private void ResetValidationResults() {
        validationPassed = false;
        eventsProcessed = 0;
        mainThreadEvents = 0;
        backgroundThreadEvents = 0;
        threadIds.Clear();
        
        Debug.Log("[ThreadNinjaFixValidator] Validation results reset");
    }
    
    /// <summary>
    /// Manual validation trigger
    /// </summary>
    [ContextMenu("Run Validation")]
    public void RunValidationManually() {
        StartCoroutine(ValidateThreadNinjaFix());
    }
    
    /// <summary>
    /// Quick thread check
    /// </summary>
    [ContextMenu("Check Current Thread")]
    public void CheckCurrentThread() {
        int currentThread = Thread.CurrentThread.ManagedThreadId;
        bool isMainThread = currentThread == _mainThreadId;
        
        Debug.Log($"[ThreadNinjaFixValidator] Current Thread: {currentThread}");
        Debug.Log($"[ThreadNinjaFixValidator] Main Thread: {_mainThreadId}");
        Debug.Log($"[ThreadNinjaFixValidator] Is Main Thread: {isMainThread}");
    }
    
    /// <summary>
    /// Test single event processing
    /// </summary>
    [ContextMenu("Test Single Event")]
    public void TestSingleEvent() {
        StartCoroutine(TestSingleEventCoroutine());
    }
    
    private IEnumerator TestSingleEventCoroutine() {
        Debug.Log("[ThreadNinjaFixValidator] Testing single event...");
        
        int threadBefore = Thread.CurrentThread.ManagedThreadId;
        Debug.Log($"Thread before: {threadBefore}");
        
        yield return StartCoroutine(AfEventProcess.ProcessAndLogEventName("single_test_event"));
        
        int threadAfter = Thread.CurrentThread.ManagedThreadId;
        Debug.Log($"Thread after: {threadAfter}");
        
        if (threadBefore == threadAfter) {
            Debug.Log("<color=green>✓ Single event test passed - no thread switching</color>");
        } else {
            Debug.Log("<color=red>✗ Single event test failed - thread switching detected</color>");
        }
    }
}
