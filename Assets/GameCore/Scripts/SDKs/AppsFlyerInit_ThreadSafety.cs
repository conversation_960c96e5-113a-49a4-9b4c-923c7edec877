using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Concurrent;
using UnityEngine;

/// <summary>
/// Thread-safe wrapper for AppsFlyer event logging to prevent ANR issues
/// Addresses JNI thread safety problems and IllegalMonitorStateException
/// </summary>
public static class AppsFlyerThreadSafety {
    
    #region Private Fields
    
    private static readonly object _queueLock = new object();
    private static readonly Queue<AppsFlyerEventData> _eventQueue = new Queue<AppsFlyerEventData>();
    private static volatile bool _isProcessing = false;
    private static readonly int _mainThreadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
    
    #endregion
    
    #region Event Data Structure
    
    [System.Serializable]
    public class AppsFlyerEventData {
        public string eventName;
        public Dictionary<string, object> parameters;
        public DateTime timestamp;
        
        public AppsFlyerEventData(string name, Dictionary<string, object> param) {
            eventName = name;
            parameters = param;
            timestamp = DateTime.Now;
        }
    }
    
    #endregion
    
    #region Public Methods
    
    /// <summary>
    /// Thread-safe method to log AppsFlyer events
    /// </summary>
    public static void LogEventSafe(string eventName, Dictionary<string, object> parameters = null) {
        if (string.IsNullOrEmpty(eventName)) {
            Debug.LogWarning("[AppsFlyerThreadSafety] Event name is null or empty");
            return;
        }
        
        // Create safe copy of parameters to avoid reference issues
        Dictionary<string, object> safeParams = null;
        if (parameters != null) {
            safeParams = new Dictionary<string, object>();
            foreach (var kvp in parameters) {
                if (kvp.Key != null && kvp.Value != null) {
                    safeParams[kvp.Key] = kvp.Value;
                }
            }
        }

        var eventData = new AppsFlyerEventData(eventName, safeParams);

        lock (_queueLock) {
            _eventQueue.Enqueue(eventData);

            // Limit queue size to prevent memory issues
            while (_eventQueue.Count > 100) {
                var oldEvent = _eventQueue.Dequeue();
                Debug.LogWarning($"[AppsFlyerThreadSafety] Queue overflow, dropping event: {oldEvent.eventName}");
            }
        }

        // Start processing if not already running
        if (!_isProcessing) {
            if (AppsFlyerInit.instanceAFI != null) {
                AppsFlyerInit.instanceAFI.StartCoroutine(ProcessEventQueue());
            } else {
                Debug.LogError("[AppsFlyerThreadSafety] AppsFlyerInit instance not available");
            }
        }
    }
    
    #endregion
    
    #region Private Methods
    
    /// <summary>
    /// Process events from queue on main thread
    /// </summary>
    private static IEnumerator ProcessEventQueue() {
        _isProcessing = true;
        
        try {
            while (true) {
                AppsFlyerEventData eventData = null;
                
                // Get next event from queue
                lock (_queueLock) {
                    if (_eventQueue.Count > 0) {
                        eventData = _eventQueue.Dequeue();
                    }
                }
                
                if (eventData == null) {
                    // No more events, exit processing
                    break;
                }
                
                // Check if event is too old (prevent processing stale events)
                if ((DateTime.Now - eventData.timestamp).TotalMinutes > 5) {
                    Debug.LogWarning($"[AppsFlyerThreadSafety] Dropping stale event: {eventData.eventName}");
                    continue;
                }
                
                // Process the event
                yield return ProcessSingleEvent(eventData);
                
                // Small delay between events to prevent overwhelming the system
                yield return new WaitForSeconds(0.1f);
            }
        } finally {
            _isProcessing = false;
        }
    }
    
    /// <summary>
    /// Process single event with error handling
    /// </summary>
    private static IEnumerator ProcessSingleEvent(AppsFlyerEventData eventData) {
        try {
            // Ensure we're on the main thread for JNI calls
            if (!IsMainThread()) {
                Debug.LogWarning($"[AppsFlyerThreadSafety] Event {eventData.eventName} not on main thread, dispatching to main thread");
                
                // Use UnityMainThreadDispatcher to ensure main thread execution
                bool eventProcessed = false;
                Exception processingException = null;
                
                if (UnityMainThreadDispatcher.Exists()) {
                    UnityMainThreadDispatcher.Instance().Enqueue(() => {
                        try {
                            ProcessEventOnMainThread(eventData);
                            eventProcessed = true;
                        } catch (Exception ex) {
                            processingException = ex;
                            eventProcessed = true;
                        }
                    });
                    
                    // Wait for main thread processing to complete
                    float timeout = 5f; // 5 second timeout
                    float startTime = Time.realtimeSinceStartup;
                    
                    while (!eventProcessed && (Time.realtimeSinceStartup - startTime) < timeout) {
                        yield return null;
                    }
                    
                    if (!eventProcessed) {
                        Debug.LogError($"[AppsFlyerThreadSafety] Timeout processing event {eventData.eventName}");
                    } else if (processingException != null) {
                        throw processingException;
                    }
                } else {
                    Debug.LogError("[AppsFlyerThreadSafety] UnityMainThreadDispatcher not available");
                }
            } else {
                // Already on main thread, process directly
                ProcessEventOnMainThread(eventData);
            }

        } catch (Exception ex) {
            Debug.LogError($"[AppsFlyerThreadSafety] Error processing event {eventData.eventName}: {ex.Message}");
            Debug.LogError($"[AppsFlyerThreadSafety] Stack trace: {ex.StackTrace}");
            
            // Log additional context for debugging
            Debug.LogError($"[AppsFlyerThreadSafety] Thread ID: {System.Threading.Thread.CurrentThread.ManagedThreadId}");
            Debug.LogError($"[AppsFlyerThreadSafety] Is Main Thread: {IsMainThread()}");
        }

        yield return null;
    }
    
    /// <summary>
    /// Process event on main thread with proper JNI handling
    /// </summary>
    private static void ProcessEventOnMainThread(AppsFlyerEventData eventData) {
        // Convert parameters to string dictionary for AppsFlyer
        Dictionary<string, string> stringParams = new Dictionary<string, string>();
        
        if (eventData.parameters != null) {
            foreach (var kvp in eventData.parameters) {
                if (kvp.Key != null && kvp.Value != null) {
                    stringParams[kvp.Key] = kvp.Value.ToString();
                }
            }
        }

        // Send event with additional safety checks
        if (Application.isEditor || Configuration.isAdmin) {
            Debug.Log($"[AppsFlyerThreadSafety] Sending event: {eventData.eventName} on thread {System.Threading.Thread.CurrentThread.ManagedThreadId}");
        }

        // Retry mechanism for JNI calls
        int maxRetries = 3;
        for (int retry = 0; retry < maxRetries; retry++) {
            try {
                AppsFlyerSDK.AppsFlyer.sendEvent(eventData.eventName, stringParams);
                break; // Success, exit retry loop
            } catch (System.Exception ex) when (retry < maxRetries - 1) {
                Debug.LogWarning($"[AppsFlyerThreadSafety] Retry {retry + 1}/{maxRetries} for event {eventData.eventName}: {ex.Message}");
                System.Threading.Thread.Sleep(100); // Brief delay before retry
            }
        }
    }
    
    /// <summary>
    /// Check if current thread is Unity main thread
    /// </summary>
    private static bool IsMainThread() {
        return System.Threading.Thread.CurrentThread.ManagedThreadId == _mainThreadId;
    }
    
    /// <summary>
    /// Clear all pending events (use in emergency situations)
    /// </summary>
    public static void ClearQueue() {
        lock (_queueLock) {
            int count = _eventQueue.Count;
            _eventQueue.Clear();
            Debug.Log($"[AppsFlyerThreadSafety] Cleared {count} pending events");
        }
    }
    
    /// <summary>
    /// Get current queue status for monitoring
    /// </summary>
    public static int GetQueueCount() {
        lock (_queueLock) {
            return _eventQueue.Count;
        }
    }
    
    #endregion
}
