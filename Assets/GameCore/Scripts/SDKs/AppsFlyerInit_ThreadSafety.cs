using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Thread-safe wrapper for AppsFlyer events to prevent JNI crashes
/// </summary>
public static class AppsFlyerThreadSafety {
    private static readonly Queue<AppsFlyerEventData> _eventQueue   = new Queue<AppsFlyerEventData>();
    private static readonly object                    _queueLock    = new object();
    private static          bool                      _isProcessing = false;

    [Serializable]
    public class AppsFlyerEventData {
        public string                     eventName;
        public Dictionary<string, object> parameters;
        public DateTime                   timestamp;

        public AppsFlyerEventData(string eventName, Dictionary<string, object> parameters) {
            this.eventName = eventName;
            this.parameters = parameters;
            this.timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// Thread-safe method to enqueue AppsFlyer events
    /// </summary>
    public static void EnqueueEvent(string eventName, Dictionary<string, object> parameters = null) {
        // Validate event name
        if (string.IsNullOrEmpty(eventName)) {
            Debug.LogWarning("[AppsFlyerThreadSafety] Event name is null or empty");
            return;
        }

        // Create safe copy of parameters
        Dictionary<string, object> safeParams = null;
        if (parameters != null) {
            safeParams = new Dictionary<string, object>();
            foreach (var kvp in parameters) {
                if (kvp.Key != null && kvp.Value != null) {
                    safeParams[kvp.Key] = kvp.Value;
                }
            }
        }

        var eventData = new AppsFlyerEventData(eventName, safeParams);

        lock (_queueLock) {
            _eventQueue.Enqueue(eventData);

            // Limit queue size to prevent memory issues
            while (_eventQueue.Count > 100) {
                var oldEvent = _eventQueue.Dequeue();
                Debug.LogWarning($"[AppsFlyerThreadSafety] Queue overflow, dropping event: {oldEvent.eventName}");
            }
        }

        // Start processing if not already running
        if (!_isProcessing) {
            if (AppsFlyerInit.instanceAFI != null) {
                AppsFlyerInit.instanceAFI.StartCoroutine(ProcessEventQueue());
            }
        }
    }

    /// <summary>
    /// Process events from queue on main thread
    /// </summary>
    private static IEnumerator ProcessEventQueue() {
        _isProcessing = true;

        while (true) {
            AppsFlyerEventData eventData = null;

            lock (_queueLock) {
                if (_eventQueue.Count > 0) {
                    eventData = _eventQueue.Dequeue();
                }
            }

            if (eventData == null) {
                // No events to process, wait and check again
                yield return new WaitForSeconds(0.1f);

                // If queue is empty for 5 seconds, stop processing
                if (_eventQueue.Count == 0) {
                    yield return new WaitForSeconds(5f);

                    if (_eventQueue.Count == 0) {
                        _isProcessing = false;
                        yield break;
                    }
                }

                continue;
            }

            // Check if event is too old (prevent stale events)
            if (DateTime.Now - eventData.timestamp > TimeSpan.FromMinutes(5)) {
                Debug.LogWarning($"[AppsFlyerThreadSafety] Dropping stale event: {eventData.eventName}");
                continue;
            }

            // Process event safely
            yield return ProcessEventSafely(eventData);

            // Small delay to prevent overwhelming the system
            yield return null;
        }
    }

    /// <summary>
    /// Safely process individual event with error handling
    /// </summary>
    private static IEnumerator ProcessEventSafely(AppsFlyerEventData eventData) {
        if (eventData == null) {
            yield break;
        }

        // Wait for AppsFlyer to be properly initialized
        while (!AppsFlyerInit.IsInited) {
            yield return new WaitForSeconds(0.5f);
        }

        try {

            // Additional safety: ensure we're on main thread
            if (!UnityEngine.Application.isPlaying) {
                Debug.LogWarning("[AppsFlyerThreadSafety] Application not playing, skipping event");
                yield break;
            }

            // Validate event data before sending
            if (string.IsNullOrEmpty(eventData.eventName)) {
                Debug.LogWarning("[AppsFlyerThreadSafety] Invalid event name");
                yield break;
            }

            // Convert parameters to string dictionary (AppsFlyer requirement)
            Dictionary<string, string> stringParams = null;
            if (eventData.parameters != null && eventData.parameters.Count > 0) {
                stringParams = new Dictionary<string, string>();
                foreach (var kvp in eventData.parameters) {
                    if (kvp.Key != null && kvp.Value != null) {
                        stringParams[kvp.Key] = kvp.Value.ToString();
                    }
                }
            }

            // Send event with try-catch
            if (Application.isEditor || Configuration.isAdmin) {
                Debug.Log($"[AppsFlyerThreadSafety] Sending event: {eventData.eventName}");
            }

            AppsFlyerSDK.AppsFlyer.sendEvent(eventData.eventName, stringParams);

        } catch (Exception ex) {
            Debug.LogError($"[AppsFlyerThreadSafety] Error processing event {eventData.eventName}: {ex.Message}");
            Debug.LogError($"[AppsFlyerThreadSafety] Stack trace: {ex.StackTrace}");
        }

        yield return null;
    }

    /// <summary>
    /// Clear all pending events (use in emergency situations)
    /// </summary>
    public static void ClearQueue() {
        lock (_queueLock) {
            int count = _eventQueue.Count;
            _eventQueue.Clear();
            Debug.Log($"[AppsFlyerThreadSafety] Cleared {count} pending events");
        }
    }

    /// <summary>
    /// Get current queue size for monitoring
    /// </summary>
    public static int GetQueueSize() {
        lock (_queueLock) {
            return _eventQueue.Count;
        }
    }
}