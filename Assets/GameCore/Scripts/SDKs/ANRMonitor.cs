using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Threading;

/// <summary>
/// ANR (Application Not Responding) Monitor to detect and prevent ANR issues
/// Specifically designed to monitor AppsFlyer JNI calls and thread safety
/// </summary>
public class ANRMonitor : MonoBehaviour {
    
    #region Singleton
    
    private static ANRMonitor _instance;
    public static ANRMonitor Instance {
        get {
            if (_instance == null) {
                GameObject go = new GameObject("ANRMonitor");
                _instance = go.AddComponent<ANRMonitor>();
                DontDestroyOnLoad(go);
            }
            return _instance;
        }
    }
    
    #endregion
    
    #region Configuration
    
    [Header("ANR Detection Settings")]
    [SerializeField] private bool enableANRDetection = true;
    [SerializeField] private float anrThresholdSeconds = 5f;
    [SerializeField] private float checkIntervalSeconds = 1f;
    
    [Header("AppsFlyer Monitoring")]
    [SerializeField] private bool monitorAppsFlyerEvents = true;
    [SerializeField] private int maxEventQueueSize = 50;
    
    #endregion
    
    #region Private Fields
    
    private Thread _anrDetectionThread;
    private volatile bool _isMainThreadResponding = true;
    private volatile float _lastMainThreadUpdate;
    private volatile bool _isMonitoring = false;
    
    // AppsFlyer event monitoring
    private Queue<ANREventInfo> _recentEvents = new Queue<ANREventInfo>();
    private readonly object _eventLock = new object();
    
    #endregion
    
    #region Event Info Structure
    
    [System.Serializable]
    public class ANREventInfo {
        public string eventName;
        public DateTime timestamp;
        public int threadId;
        public string stackTrace;
        
        public ANREventInfo(string name, int thread) {
            eventName = name;
            timestamp = DateTime.Now;
            threadId = thread;
            stackTrace = Environment.StackTrace;
        }
    }
    
    #endregion
    
    #region Unity Lifecycle
    
    private void Awake() {
        if (_instance == null) {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeMonitoring();
        } else if (_instance != this) {
            Destroy(gameObject);
        }
    }
    
    private void Start() {
        StartCoroutine(MainThreadHeartbeat());
    }
    
    private void Update() {
        _lastMainThreadUpdate = Time.realtimeSinceStartup;
        _isMainThreadResponding = true;
    }
    
    private void OnDestroy() {
        StopMonitoring();
    }
    
    private void OnApplicationPause(bool pauseStatus) {
        if (pauseStatus) {
            StopMonitoring();
        } else {
            InitializeMonitoring();
        }
    }
    
    #endregion
    
    #region ANR Detection
    
    private void InitializeMonitoring() {
        if (!enableANRDetection || _isMonitoring) return;
        
        _isMonitoring = true;
        _lastMainThreadUpdate = Time.realtimeSinceStartup;
        
        _anrDetectionThread = new Thread(ANRDetectionLoop) {
            Name = "ANRDetectionThread",
            IsBackground = true
        };
        _anrDetectionThread.Start();
        
        Debug.Log("[ANRMonitor] ANR detection started");
    }
    
    private void StopMonitoring() {
        _isMonitoring = false;
        
        if (_anrDetectionThread != null && _anrDetectionThread.IsAlive) {
            _anrDetectionThread.Join(1000); // Wait up to 1 second
        }
        
        Debug.Log("[ANRMonitor] ANR detection stopped");
    }
    
    private void ANRDetectionLoop() {
        while (_isMonitoring) {
            try {
                Thread.Sleep((int)(checkIntervalSeconds * 1000));
                
                float timeSinceLastUpdate = Time.realtimeSinceStartup - _lastMainThreadUpdate;
                
                if (timeSinceLastUpdate > anrThresholdSeconds) {
                    OnANRDetected(timeSinceLastUpdate);
                }
                
            } catch (ThreadInterruptedException) {
                break;
            } catch (Exception ex) {
                Debug.LogError($"[ANRMonitor] Error in ANR detection thread: {ex.Message}");
            }
        }
    }
    
    private void OnANRDetected(float duration) {
        Debug.LogError($"[ANRMonitor] ANR DETECTED! Main thread unresponsive for {duration:F2} seconds");
        
        // Log recent AppsFlyer events
        LogRecentEvents();
        
        // Try to recover
        TryRecoverFromANR();
    }
    
    #endregion
    
    #region AppsFlyer Event Monitoring
    
    public void LogAppsFlyerEvent(string eventName) {
        if (!monitorAppsFlyerEvents) return;
        
        lock (_eventLock) {
            var eventInfo = new ANREventInfo(eventName, Thread.CurrentThread.ManagedThreadId);
            _recentEvents.Enqueue(eventInfo);
            
            // Keep only recent events
            while (_recentEvents.Count > maxEventQueueSize) {
                _recentEvents.Dequeue();
            }
        }
        
        Debug.Log($"[ANRMonitor] AppsFlyer event logged: {eventName} on thread {Thread.CurrentThread.ManagedThreadId}");
    }
    
    private void LogRecentEvents() {
        lock (_eventLock) {
            Debug.LogWarning($"[ANRMonitor] Recent AppsFlyer events ({_recentEvents.Count}):");
            
            foreach (var eventInfo in _recentEvents) {
                Debug.LogWarning($"[ANRMonitor] - {eventInfo.eventName} at {eventInfo.timestamp:HH:mm:ss.fff} on thread {eventInfo.threadId}");
            }
        }
    }
    
    #endregion
    
    #region Recovery Methods
    
    private void TryRecoverFromANR() {
        Debug.LogWarning("[ANRMonitor] Attempting ANR recovery...");
        
        // Clear AppsFlyer event queue
        AppsFlyerThreadSafety.ClearQueue();
        
        // Force garbage collection
        System.GC.Collect();
        
        // Reset monitoring
        _isMainThreadResponding = true;
        _lastMainThreadUpdate = Time.realtimeSinceStartup;
        
        Debug.LogWarning("[ANRMonitor] ANR recovery attempted");
    }
    
    #endregion
    
    #region Coroutines
    
    private IEnumerator MainThreadHeartbeat() {
        while (true) {
            yield return new WaitForSeconds(0.5f);
            
            // This coroutine ensures the main thread is responsive
            _isMainThreadResponding = true;
        }
    }
    
    #endregion
    
    #region Public API
    
    public bool IsMainThreadResponding() {
        return _isMainThreadResponding;
    }
    
    public int GetRecentEventCount() {
        lock (_eventLock) {
            return _recentEvents.Count;
        }
    }
    
    public void ClearEventHistory() {
        lock (_eventLock) {
            _recentEvents.Clear();
        }
        Debug.Log("[ANRMonitor] Event history cleared");
    }
    
    #endregion
}
