using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Threading;

/// <summary>
/// Test script to validate ANR fix implementation
/// Use this to test thread safety and performance of the AppsFlyer fix
/// </summary>
public class ANRFixTester : MonoBehaviour {
    
    [Header("Test Configuration")]
    [SerializeField] private bool runTestsOnStart = false;
    [SerializeField] private int stressTestEventCount = 100;
    [SerializeField] private float stressTestInterval = 0.1f;
    
    [Header("Test Results")]
    [SerializeField] private int eventsProcessed = 0;
    [SerializeField] private int errorsEncountered = 0;
    [SerializeField] private float lastTestDuration = 0f;
    
    private void Start() {
        if (runTestsOnStart) {
            StartCoroutine(RunAllTests());
        }
    }
    
    #region Test Methods
    
    /// <summary>
    /// Run all validation tests
    /// </summary>
    public IEnumerator RunAllTests() {
        Debug.Log("[ANRFixTester] Starting comprehensive ANR fix tests...");
        
        yield return StartCoroutine(TestBasicFunctionality());
        yield return new WaitForSeconds(1f);
        
        yield return StartCoroutine(TestThreadSafety());
        yield return new WaitForSeconds(1f);
        
        yield return StartCoroutine(TestStressLoad());
        yield return new WaitForSeconds(1f);
        
        yield return StartCoroutine(TestErrorHandling());
        
        Debug.Log("[ANRFixTester] All tests completed!");
        LogTestResults();
    }
    
    /// <summary>
    /// Test basic AppsFlyer event logging functionality
    /// </summary>
    public IEnumerator TestBasicFunctionality() {
        Debug.Log("[ANRFixTester] Testing basic functionality...");
        
        try {
            // Test simple event
            AppsFlyerInit.LogEventSafe("test_basic_event");
            eventsProcessed++;
            
            // Test event with parameters
            Dictionary<string, object> parameters = new Dictionary<string, object> {
                { "test_param", "test_value" },
                { "test_number", 123 },
                { "test_bool", true }
            };
            
            AppsFlyerInit.LogEventSafe("test_event_with_params", parameters);
            eventsProcessed++;
            
            Debug.Log("[ANRFixTester] Basic functionality test passed");
            
        } catch (System.Exception ex) {
            Debug.LogError($"[ANRFixTester] Basic functionality test failed: {ex.Message}");
            errorsEncountered++;
        }
        
        yield return new WaitForSeconds(0.5f);
    }
    
    /// <summary>
    /// Test thread safety by calling from background threads
    /// </summary>
    public IEnumerator TestThreadSafety() {
        Debug.Log("[ANRFixTester] Testing thread safety...");
        
        int threadTestCount = 10;
        int completedThreads = 0;
        
        for (int i = 0; i < threadTestCount; i++) {
            int threadIndex = i;
            
            Thread testThread = new Thread(() => {
                try {
                    // Call from background thread - should be handled safely
                    AppsFlyerInit.LogEventSafe($"thread_test_event_{threadIndex}");
                    
                    // Test with parameters from background thread
                    Dictionary<string, object> threadParams = new Dictionary<string, object> {
                        { "thread_id", Thread.CurrentThread.ManagedThreadId },
                        { "test_index", threadIndex }
                    };
                    
                    AppsFlyerInit.LogEventSafe($"thread_param_test_{threadIndex}", threadParams);
                    
                    Interlocked.Increment(ref completedThreads);
                    Interlocked.Increment(ref eventsProcessed);
                    
                } catch (System.Exception ex) {
                    Debug.LogError($"[ANRFixTester] Thread {threadIndex} failed: {ex.Message}");
                    Interlocked.Increment(ref errorsEncountered);
                }
            });
            
            testThread.Start();
        }
        
        // Wait for all threads to complete
        float timeout = 10f;
        float startTime = Time.realtimeSinceStartup;
        
        while (completedThreads < threadTestCount && (Time.realtimeSinceStartup - startTime) < timeout) {
            yield return new WaitForSeconds(0.1f);
        }
        
        if (completedThreads == threadTestCount) {
            Debug.Log("[ANRFixTester] Thread safety test passed");
        } else {
            Debug.LogError($"[ANRFixTester] Thread safety test failed - only {completedThreads}/{threadTestCount} completed");
            errorsEncountered++;
        }
    }
    
    /// <summary>
    /// Test system under high event load
    /// </summary>
    public IEnumerator TestStressLoad() {
        Debug.Log($"[ANRFixTester] Testing stress load with {stressTestEventCount} events...");
        
        float startTime = Time.realtimeSinceStartup;
        int initialQueueSize = AppsFlyerThreadSafety.GetQueueCount();
        
        try {
            for (int i = 0; i < stressTestEventCount; i++) {
                Dictionary<string, object> stressParams = new Dictionary<string, object> {
                    { "stress_index", i },
                    { "timestamp", System.DateTime.Now.Ticks },
                    { "batch_id", "stress_test_batch" }
                };
                
                AppsFlyerInit.LogEventSafe($"stress_test_event_{i}", stressParams);
                eventsProcessed++;
                
                if (stressTestInterval > 0) {
                    yield return new WaitForSeconds(stressTestInterval);
                }
                
                // Check for queue overflow
                if (i % 20 == 0) {
                    int currentQueueSize = AppsFlyerThreadSafety.GetQueueCount();
                    Debug.Log($"[ANRFixTester] Stress test progress: {i}/{stressTestEventCount}, Queue size: {currentQueueSize}");
                }
            }
            
            lastTestDuration = Time.realtimeSinceStartup - startTime;
            Debug.Log($"[ANRFixTester] Stress test completed in {lastTestDuration:F2} seconds");
            
        } catch (System.Exception ex) {
            Debug.LogError($"[ANRFixTester] Stress test failed: {ex.Message}");
            errorsEncountered++;
        }
        
        // Wait for queue to process
        yield return new WaitForSeconds(2f);
        
        int finalQueueSize = AppsFlyerThreadSafety.GetQueueCount();
        Debug.Log($"[ANRFixTester] Queue size after stress test: {finalQueueSize}");
    }
    
    /// <summary>
    /// Test error handling with invalid inputs
    /// </summary>
    public IEnumerator TestErrorHandling() {
        Debug.Log("[ANRFixTester] Testing error handling...");
        
        try {
            // Test null event name
            AppsFlyerInit.LogEventSafe(null);
            
            // Test empty event name
            AppsFlyerInit.LogEventSafe("");
            
            // Test null parameters (should be handled gracefully)
            AppsFlyerInit.LogEventSafe("test_null_params", null);
            
            // Test parameters with null values
            Dictionary<string, object> nullValueParams = new Dictionary<string, object> {
                { "valid_param", "valid_value" },
                { "null_param", null },
                { null, "null_key" }
            };
            
            AppsFlyerInit.LogEventSafe("test_null_values", nullValueParams);
            
            Debug.Log("[ANRFixTester] Error handling test completed");
            
        } catch (System.Exception ex) {
            Debug.LogError($"[ANRFixTester] Error handling test failed: {ex.Message}");
            errorsEncountered++;
        }
        
        yield return new WaitForSeconds(0.5f);
    }
    
    #endregion
    
    #region Utility Methods
    
    /// <summary>
    /// Log comprehensive test results
    /// </summary>
    private void LogTestResults() {
        Debug.Log("=== ANR Fix Test Results ===");
        Debug.Log($"Events Processed: {eventsProcessed}");
        Debug.Log($"Errors Encountered: {errorsEncountered}");
        Debug.Log($"Last Test Duration: {lastTestDuration:F2}s");
        Debug.Log($"Current Queue Size: {AppsFlyerThreadSafety.GetQueueCount()}");
        
        if (ANRMonitor.Instance != null) {
            Debug.Log($"ANR Monitor Events: {ANRMonitor.Instance.GetRecentEventCount()}");
            Debug.Log($"Main Thread Responsive: {ANRMonitor.Instance.IsMainThreadResponding()}");
        }
        
        Debug.Log("============================");
        
        // Determine overall test result
        if (errorsEncountered == 0) {
            Debug.Log("<color=green>[ANRFixTester] ALL TESTS PASSED!</color>");
        } else {
            Debug.Log($"<color=red>[ANRFixTester] {errorsEncountered} TESTS FAILED!</color>");
        }
    }
    
    /// <summary>
    /// Reset test counters
    /// </summary>
    [ContextMenu("Reset Test Results")]
    public void ResetTestResults() {
        eventsProcessed = 0;
        errorsEncountered = 0;
        lastTestDuration = 0f;
        
        Debug.Log("[ANRFixTester] Test results reset");
    }
    
    /// <summary>
    /// Run tests manually from inspector
    /// </summary>
    [ContextMenu("Run All Tests")]
    public void RunTestsManually() {
        StartCoroutine(RunAllTests());
    }
    
    /// <summary>
    /// Clear all queues and reset system
    /// </summary>
    [ContextMenu("Clear System State")]
    public void ClearSystemState() {
        AppsFlyerThreadSafety.ClearQueue();
        
        if (ANRMonitor.Instance != null) {
            ANRMonitor.Instance.ClearEventHistory();
        }
        
        Debug.Log("[ANRFixTester] System state cleared");
    }
    
    #endregion
}
