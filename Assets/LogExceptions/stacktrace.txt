Crashed: Thread #1
SIGABRT 0x0000000000000000
0
libc.so
(Missing BuildId a9682a43d4afba2f7ad4dbb2a45a3a46)
6
libart.so
(Missing BuildId 4ccb65ae9ac5ad5da3af5a342d5b0b92)
7
libunity.so
AndroidJNIBindingsHelpers::FindClass(core::basic_string<char, core::StringStorageDefault<char> > const&)
8
std::__ndk1::__tree_iterator<DelayedCallManager::Callback, std::__ndk1::__tree_node<DelayedCallManager::Callback, void*>*, long> std::__ndk1::__tree<DelayedCallManager::Callback, std::__ndk1::less<DelayedCallManager::Callback>, memory_pool<DelayedCallManager::Callback> >::__emplace_multi<DelayedCallManager::Callback const&>(DelayedCallManager::Callback const&)
9
scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
10
emutls.c - Line 249
__emutls_register_common + 249
11
12
ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
13
Coroutine::InvokeMoveNext(ScriptingExceptionPtr*)
14
15
16
Coroutine::Run(bool*)
17
Coroutine::CleanupCoroutine(void*)
18
DelayedCallManager::Update(int)
19
Coroutine::SetCurrentMethod(ScriptingMethodPtr)
20
ExecutePlayerLoop(NativePlayerLoopSystem*)
21
Scripting::UnityEngine::Rendering::OnDemandRenderingProxy::GetRenderFrameInterval(int*, ScriptingExceptionPtr*)
22
23
24
25
26
27
28
29
30
31
32
33
34
35
PlayerLoop()
36
AndroidAssetPacks::AssetPackManager::UpdateCoreAssetPacksStatus()
37
38
39
40
41
42
43
UnityPlayerLoop()
44
nativeRender(_JNIEnv*, _jobject*)
45
(Missing)
48