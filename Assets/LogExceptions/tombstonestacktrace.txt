# Crashlytics - Tombstone Stack Trace
# Application: com.amanotes.beathopper
# Platform: android
# Version: 7.6.9 (25060517)
# Issue: 81d91f82fb31a73b8512835ad952f2d3
# Session: 68468b30021400017d5180429a4ebccf_DNE_0_v2
# Date: Mon Jun 09 2025 14:24:42 GMT+0700 (Indochina Time)

No pending exception expected: java.lang.IllegalMonitorStateException: object not locked by thread before notify()
(Throwable with no stack trace):
#00 pc 0x5b690 (abort [/apex/com.android.runtime/lib64/bionic/libc.so]) (BuildId: a9682a43d4afba2f7ad4dbb2a45a3a46)
#01 pc 0x933abc (art::Runtime::Abort(char const*) [/apex/com.android.art/lib64/libart.so]) (BuildId: 4ccb65ae9ac5ad5da3af5a342d5b0b92)
#02 pc 0x160fc (android::base::SetAborter(std::__1::function<void (char const*)>&&)::$_0::__invoke(char const*) [/apex/com.android.art/lib64/libbase.so]) (BuildId: 1470f61c05962eb04fafe76bd58bf664)
#03 pc 0x156d0 (android::base::LogMessage::~LogMessage() [/apex/com.android.art/lib64/libbase.so]) (BuildId: 1470f61c05962eb04fafe76bd58bf664)
#04 pc 0x24eb18 (art::ClassLinker::FindClass(art::Thread*, char const*, art::Handle<art::mirror::ClassLoader>) [/apex/com.android.art/lib64/libart.so]) (BuildId: 4ccb65ae9ac5ad5da3af5a342d5b0b92)
#05 pc 0x73bbf8 (art::JNI<false>::FindClass(_JNIEnv*, char const*) [/apex/com.android.art/lib64/libart.so]) (BuildId: 4ccb65ae9ac5ad5da3af5a342d5b0b92)
#06 pc 0x10f820 (AndroidJNIBindingsHelpers::FindClass(core::basic_string<char, core::StringStorageDefault<char> > const&) [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#07 pc 0x115030 (AndroidJNI_CUSTOM_FindClass(ScriptingBackendNativeStringPtrOpaque*) [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#08 pc 0x22cad50 (AndroidJNISafe_FindClass_m2E8072B600873B4D87B2197C1168967050208D1B [UnityEngine.AndroidJNIModule.cpp:11681]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#09 pc 0x22caf40 (AndroidJavaObject__AndroidJavaObject_m1284CB7198514B8C06A2BF794ACDC909DC26443F [UnityEngine.AndroidJNIModule.cpp:5421]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#10 pc 0xabce04 (AppsFlyerAndroid_convertDictionaryToJavaMap_m23493BDF8EFBA83663D301E7AA301632055F8E7F [AppsFlyer.cpp:9754]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#11 pc 0xabdd04 (AppsFlyerAndroid_sendEvent_m2AFC0888C6B1D352B7E0F60DAF41CF0CE96FA183 [AppsFlyer.cpp:8233]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#12 pc 0xb92c58 (AppsFlyerInit_LogEvent_m0C85CB8EAA2996647469AB8F0EE5010977F398CB [Assembly-CSharp__15.cpp:32642]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#13 pc 0xb92f48 (U3CProcessAndLogEventNameU3Ed__8_MoveNext_m284088E4DEA1499CD5D85447FD7B04E9404110CD [Assembly-CSharp__15.cpp:16399]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#14 pc 0xe620d0 (Task_MoveNextUnity_m091617EA89E0AE975B9B333755C85408518BB795 [Assembly-CSharp__42.cpp:119]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#15 pc 0xe61d90 (Task_OnMoveNext_mA8A637DE675DED0D1F9A77C3E4D951ACF06EED4D [Assembly-CSharp__42.cpp:9588]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#16 pc 0x2307bac (SetupCoroutine_InvokeMoveNext_m72FC77384CAC3133B6EE650E0581D055B34B2F5F [UnityEngine.CoreModule__1.cpp:151]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#17 pc 0x95232c (il2cpp::vm::Runtime::InvokeWithThrow(MethodInfo const*, void*, void**) [Runtime.cpp:582]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#18 pc 0x9521a0 (il2cpp::vm::Runtime::Invoke(MethodInfo const*, void*, void**, Il2CppException**) [Runtime.cpp:568]) (BuildId: 3abb0493f06146370a0af8e3af466e8b032f174a)
#19 pc 0x2f319c (scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool) [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#20 pc 0x301808 (ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool) [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#21 pc 0x30d738 (Coroutine::InvokeMoveNext(ScriptingExceptionPtr*) [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#22 pc 0x30d314 (Coroutine::Run(bool*) [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#23 pc 0x1877e8 (DelayedCallManager::Update(int) [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#24 pc 0x22bcd8 (ExecutePlayerLoop(NativePlayerLoopSystem*) [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#25 pc 0x22bd18 (ExecutePlayerLoop(NativePlayerLoopSystem*) [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#26 pc 0x22bf50 (PlayerLoop() [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#27 pc 0x37eaac (UnityPlayerLoop() [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#28 pc 0x396058 (nativeRender(_JNIEnv*, _jobject*) [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/lib/arm64/libunity.so]) (BuildId: 8a7db8badba636917da9b92c23d689e8b4001f6d)
#29 pc 0x31e5d0 (art_jni_trampoline [/data/misc/apexdata/com.android.art/dalvik-cache/arm64/boot.oat])
#30 pc 0x20431f0 (com.unity3d.player.x0.handleMessage [/memfd:jit-cache (deleted)])
#31 pc 0x94c9ec (android.os.Handler.dispatchMessage [/data/misc/apexdata/com.android.art/dalvik-cache/arm64/boot.oat])
#32 pc 0x782420 (nterp_helper [/apex/com.android.art/lib64/libart.so]) (BuildId: 4ccb65ae9ac5ad5da3af5a342d5b0b92)
#33 pc 0x21faee (android.os.Looper.loopOnce [/system/framework/framework.jar])
#34 pc 0x9508b0 (android.os.Looper.loop [/data/misc/apexdata/com.android.art/dalvik-cache/arm64/boot.oat])
#35 pc 0x781508 (nterp_helper [/apex/com.android.art/lib64/libart.so]) (BuildId: 4ccb65ae9ac5ad5da3af5a342d5b0b92)
#36 pc 0x417674 (com.unity3d.player.y0.run [/data/app/~~_IWFSPiiuiMv7ll9fu5zMw==/com.amanotes.beathopper-v4pw8D_ngFH17z1jjIv09A==/base.apk])
#37 pc 0x368774 (art_quick_invoke_stub [/apex/com.android.art/lib64/libart.so]) (BuildId: 4ccb65ae9ac5ad5da3af5a342d5b0b92)
#38 pc 0x353f24 (art::ArtMethod::Invoke(art::Thread*, unsigned int*, unsigned int, art::JValue*, char const*) [/apex/com.android.art/lib64/libart.so]) (BuildId: 4ccb65ae9ac5ad5da3af5a342d5b0b92)
#39 pc 0x947748 (art::detail::ShortyTraits<(char)86>::Type art::ArtMethod::InvokeInstance<(char)86>(art::Thread*, art::ObjPtr<art::mirror::Object>, art::detail::ShortyTraits<>::Type...) [/apex/com.android.art/lib64/libart.so]) (BuildId: 4ccb65ae9ac5ad5da3af5a342d5b0b92)
#40 pc 0x636e80 (art::Thread::CreateCallback(void*) [/apex/com.android.art/lib64/libart.so]) (BuildId: 4ccb65ae9ac5ad5da3af5a342d5b0b92)
#41 pc 0x636930 (art::Thread::CreateCallbackWithUffdGc(void*) [/apex/com.android.art/lib64/libart.so]) (BuildId: 4ccb65ae9ac5ad5da3af5a342d5b0b92)
#42 pc 0xc3684 (__pthread_start(void*) [/apex/com.android.runtime/lib64/bionic/libc.so]) (BuildId: a9682a43d4afba2f7ad4dbb2a45a3a46)
#43 pc 0x5cfe4 (__start_thread [/apex/com.android.runtime/lib64/bionic/libc.so]) (BuildId: a9682a43d4afba2f7ad4dbb2a45a3a46)